import 'package:flutter/material.dart';
import '../../models/community_notice/community_notice.dart';
import '../../services/community_notice/community_notice_service.dart';
import '../../services/property/property_service.dart';
import '../../widgets/error_dialog.dart';
import 'create_notice_screen.dart';
import 'notice_detail_screen.dart';

class CommunityNoticesScreen extends StatefulWidget {
  final String? propertyId;
  
  const CommunityNoticesScreen({
    super.key,
    this.propertyId,
  });

  @override
  State<CommunityNoticesScreen> createState() => _CommunityNoticesScreenState();
}

class _CommunityNoticesScreenState extends State<CommunityNoticesScreen> {
  final CommunityNoticeService _noticeService = CommunityNoticeService();
  final PropertyService _propertyService = PropertyService();
  final TextEditingController _searchController = TextEditingController();
  
  List<CommunityNotice> _notices = [];
  List<CommunityNotice> _filteredNotices = [];
  List<Map<String, dynamic>> _properties = [];
  String? _selectedPropertyId;
  List<String> _selectedPropertyIds = [];
  NoticeType? _selectedType;
  NoticePriority? _selectedPriority;
  bool _isLoading = true;
  bool _isLoadingProperties = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedPropertyId = widget.propertyId;
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      // Load properties for filter dropdown
      final properties = await _propertyService.getAllProperties();
      _properties = properties.map((p) => {
        'id': p.id,
        'name': p.name,
      }).toList();
      setState(() => _isLoadingProperties = false);

      // Load notices
      await _loadNotices();
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ErrorDialog(
            title: 'Error Loading Data',
            message: e.toString(),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadNotices() async {
    try {
      List<CommunityNotice> notices;

      if (_selectedPropertyId != null) {
        notices = await _noticeService.getNoticesForProperty(_selectedPropertyId!);
      } else {
        notices = await _noticeService.getNoticesForUser();
      }

      setState(() {
        _notices = notices;
        _applyFilters();
      });
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ErrorDialog(
            title: 'Error Loading Notices',
            message: e.toString(),
          ),
        );
      }
    }
  }



  void _applyFilters() {
    List<CommunityNotice> filtered = List.from(_notices);
    
    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((notice) =>
        notice.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        notice.content.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }
    
    // Apply type filter
    if (_selectedType != null) {
      filtered = filtered.where((notice) => notice.type == _selectedType).toList();
    }
    
    // Apply priority filter
    if (_selectedPriority != null) {
      filtered = filtered.where((notice) => notice.priority == _selectedPriority).toList();
    }
    
    setState(() {
      _filteredNotices = filtered;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFilters();
  }

  void _onPropertyChanged(List<String> propertyIds) {
    setState(() {
      _selectedPropertyIds = propertyIds;
    });
    _loadNotices();
  }

  void _onTypeChanged(NoticeType? type) {
    setState(() {
      _selectedType = type;
    });
    _applyFilters();
  }

  void _onPriorityChanged(NoticePriority? priority) {
    setState(() {
      _selectedPriority = priority;
    });
    _applyFilters();
  }

  void _clearFilters() {
    setState(() {
      _selectedPropertyIds.clear();
      _selectedType = null;
      _selectedPriority = null;
      _searchQuery = '';
      _searchController.clear();
    });
    _loadNotices(); // Reload to get all notices
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        title: Text(
          'Community Notices',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_circle_outline, color: Colors.white),
            onPressed: _createNotice,
            tooltip: 'Create Notice',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: _buildNoticesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 13),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          Container(
            margin: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              decoration: InputDecoration(
                hintText: 'Search notices...',
                hintStyle: const TextStyle(color: Colors.white70, fontSize: 16),
                prefixIcon: const Icon(Icons.search, color: Colors.white70),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Colors.white70),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: _onSearchChanged,
            ),
          ),
          
          // Filters row
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // Property filter
                if (!_isLoadingProperties && _properties.isNotEmpty) ...[
                  _buildFilterChip(
                    icon: Icons.location_on_outlined,
                    label: _selectedPropertyIds.isEmpty
                        ? 'All Properties'
                        : '${_selectedPropertyIds.length} Selected',
                    isSelected: _selectedPropertyIds.isNotEmpty,
                    onTap: _showPropertyFilterDialog,
                  ),
                  const SizedBox(width: 8),
                ],

                // Type filter
                _buildFilterDropdown<NoticeType>(
                  icon: Icons.category_outlined,
                  value: _selectedType,
                  hint: 'Type',
                  items: NoticeType.values.map((type) => DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  )).toList(),
                  onChanged: _onTypeChanged,
                ),
                const SizedBox(width: 8),

                // Priority filter
                _buildFilterDropdown<NoticePriority>(
                  icon: Icons.priority_high_outlined,
                  value: _selectedPriority,
                  hint: 'Priority',
                  items: NoticePriority.values.map((priority) => DropdownMenuItem(
                    value: priority,
                    child: Text(priority.displayName),
                  )).toList(),
                  onChanged: _onPriorityChanged,
                ),

                // Clear filters button
                if (_selectedPropertyIds.isNotEmpty || _selectedType != null || _selectedPriority != null || _searchQuery.isNotEmpty) ...[
                  const SizedBox(width: 16),
                  _buildFilterChip(
                    icon: Icons.clear_all,
                    label: 'Clear All',
                    isSelected: true,
                    onTap: _clearFilters,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoticesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredNotices.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadNotices,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredNotices.length,
        itemBuilder: (context, index) {
          final notice = _filteredNotices[index];
          return _buildNoticeCard(notice);
        },
      ),
    );
  }

  Widget _buildFilterChip({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 51),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: Colors.white,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterDropdown<T>({
    required IconData icon,
    required T? value,
    required String hint,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 51),
        borderRadius: BorderRadius.circular(20),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.white,
          ),
          const SizedBox(width: 6),
          Theme(
            data: Theme.of(context).copyWith(
              canvasColor: Theme.of(context).primaryColor,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<T>(
                value: value,
                hint: Text(
                  hint,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                items: [
                  DropdownMenuItem<T>(
                    value: null,
                    child: Text(
                      'All $hint',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  ...items.map((item) => DropdownMenuItem<T>(
                    value: item.value,
                    child: Text(
                      (item.child as Text).data!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )),
                ],
                onChanged: onChanged,
                dropdownColor: Theme.of(context).primaryColor,
                icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoticeCard(CommunityNotice notice) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.withValues(alpha: 51)),
      ),
      color: Colors.white,
      child: InkWell(
        onTap: () => _viewNoticeDetail(notice),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          notice.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        if (notice.propertyName != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                notice.isCombinedPropertyNotice
                                    ? Icons.business_outlined
                                    : Icons.location_on_outlined,
                                size: 14,
                                color: Colors.grey[600]
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      notice.isCombinedPropertyNotice
                                          ? notice.propertyDisplayText
                                          : notice.propertyName!,
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 12,
                                        fontWeight: notice.isCombinedPropertyNotice
                                            ? FontWeight.w500
                                            : FontWeight.normal,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (notice.isCombinedPropertyNotice) ...[
                                      const SizedBox(height: 1),
                                      Text(
                                        '${notice.propertyCount} properties',
                                        style: TextStyle(
                                          color: Colors.grey[500],
                                          fontSize: 10,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  FutureBuilder<bool>(
                    future: _noticeService.canEditNotice(notice.id),
                    builder: (context, snapshot) {
                      if (snapshot.hasData && snapshot.data == true) {
                        return PopupMenuButton<String>(
                          icon: Icon(
                            Icons.more_vert,
                            color: Colors.grey[700],
                          ),
                          onSelected: (action) => _handleNoticeAction(action, notice),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit_outlined, size: 18),
                                  SizedBox(width: 8),
                                  Text('Edit'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete_outline, color: Colors.red, size: 18),
                                  SizedBox(width: 8),
                                  Text('Delete', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 25),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      notice.type.displayName,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withValues(alpha: 25),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      notice.priority.displayName,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatDate(notice.createdAt),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                notice.content,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.5,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              if (notice.expiresAt != null) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      notice.isExpired ? Icons.error_outline : Icons.schedule,
                      size: 14,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      notice.isExpired
                          ? 'Expired ${_formatDate(notice.expiresAt!)}'
                          : 'Expires ${_formatDate(notice.expiresAt!)}',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleNoticeAction(String action, CommunityNotice notice) async {
    switch (action) {
      case 'edit':
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CreateNoticeScreen(
              notice: notice,
            ),
          ),
        );
        if (result == true) {
          _loadNotices();
        }
        break;
      case 'delete':
        _showDeleteConfirmation(notice);
        break;
    }
  }

  void _showDeleteConfirmation(CommunityNotice notice) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notice'),
        content: const Text(
          'Are you sure you want to delete this notice? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNotice(notice);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteNotice(CommunityNotice notice) async {
    setState(() => _isLoading = true);

    try {
      await _noticeService.deleteNotice(notice.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notice deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        _loadNotices();
      }
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ErrorDialog(
            title: 'Error Deleting Notice',
            message: e.toString(),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.campaign_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty || _selectedPropertyIds.isNotEmpty || _selectedType != null || _selectedPriority != null
                ? 'No notices match your filters'
                : 'No notices yet',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedPropertyIds.isNotEmpty || _selectedType != null || _selectedPriority != null
                ? 'Try adjusting your search or filters'
                : 'Create your first community notice',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _createNotice,
            icon: const Icon(Icons.add),
            label: const Text('Create Notice'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _createNotice() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateNoticeScreen(
          propertyId: _selectedPropertyIds.isNotEmpty ? _selectedPropertyIds.first : null,
        ),
      ),
    );

    if (result == true) {
      _loadNotices();
    }
  }

  void _showPropertyFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => PropertyFilterDialog(
        properties: _properties,
        selectedPropertyIds: List.from(_selectedPropertyIds),
        onSelectionChanged: _onPropertyChanged,
      ),
    );
  }

  void _viewNoticeDetail(CommunityNotice notice) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoticeDetailScreen(notice: notice),
      ),
    );

    if (result == true) {
      _loadNotices();
    }
  }
}

class PropertyFilterDialog extends StatefulWidget {
  final List<Map<String, dynamic>> properties;
  final List<String> selectedPropertyIds;
  final Function(List<String>) onSelectionChanged;

  const PropertyFilterDialog({
    super.key,
    required this.properties,
    required this.selectedPropertyIds,
    required this.onSelectionChanged,
  });

  @override
  State<PropertyFilterDialog> createState() => _PropertyFilterDialogState();
}

class _PropertyFilterDialogState extends State<PropertyFilterDialog> {
  late List<String> _tempSelectedIds;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tempSelectedIds = List.from(widget.selectedPropertyIds);
  }

  @override
  Widget build(BuildContext context) {
    final filteredProperties = widget.properties.where((property) {
      final name = property['name'] as String;
      return name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return AlertDialog(
      title: const Text('Filter by Properties'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Search properties...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Select all/none buttons
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds = filteredProperties
                          .map((p) => p['id'] as String)
                          .toList();
                    });
                  },
                  child: const Text('Select All'),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds.clear();
                    });
                  },
                  child: const Text('Select None'),
                ),
                const Spacer(),
                Text(
                  '${_tempSelectedIds.length} selected',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Properties list
            Expanded(
              child: ListView.builder(
                itemCount: filteredProperties.length,
                itemBuilder: (context, index) {
                  final property = filteredProperties[index];
                  final propertyId = property['id'] as String;
                  final propertyName = property['name'] as String;
                  final isSelected = _tempSelectedIds.contains(propertyId);

                  return CheckboxListTile(
                    title: Text(propertyName),
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _tempSelectedIds.add(propertyId);
                        } else {
                          _tempSelectedIds.remove(propertyId);
                        }
                      });
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onSelectionChanged(_tempSelectedIds);
            Navigator.pop(context);
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}
