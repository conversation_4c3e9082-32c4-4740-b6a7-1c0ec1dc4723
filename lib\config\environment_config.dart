import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../utils/logger.dart';

class EnvironmentConfig {
  static bool _isInitialized = false;

  /// Initialize the environment configuration
  static Future<void> initialize() async {
    if (_isInitialized) {
      AppLogger.info('Environment config already initialized');
      return;
    }

    try {
      await dotenv.load(fileName: '.env');
      _isInitialized = true;
      AppLogger.info('Environment config initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to load environment config: $e');
      // Don't throw here - we'll handle missing values in getters
    }
  }

  /// Get Supabase URL from environment
  static String get supabaseUrl {
    final url = dotenv.env['SUPABASE_URL'];
    if (url == null || url.isEmpty) {
      throw Exception('SUPABASE_URL not found in environment configuration');
    }
    return url;
  }

  /// Get Supabase anonymous key from environment
  static String get supabaseAnonKey {
    final key = dotenv.env['SUPABASE_ANON_KEY'];
    if (key == null || key.isEmpty) {
      throw Exception('SUPABASE_ANON_KEY not found in environment configuration');
    }
    return key;
  }

  /// Check if environment is properly configured
  static bool get isConfigured {
    return _isInitialized && 
           dotenv.env['SUPABASE_URL'] != null && 
           dotenv.env['SUPABASE_ANON_KEY'] != null;
  }

  /// Get all environment variables (for debugging - be careful with sensitive data)
  static Map<String, String> get allVariables {
    return Map<String, String>.from(dotenv.env);
  }
}
