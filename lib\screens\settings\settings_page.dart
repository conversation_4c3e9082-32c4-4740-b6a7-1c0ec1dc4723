import 'package:currency_picker/currency_picker.dart';
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../services/service_locator.dart';
import '../legal/terms_and_conditions_screen.dart';
import '../legal/privacy_policy_screen.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> with SingleTickerProviderStateMixin {
  late Currency _selectedCurrency;
  late Locale _selectedLocale;
  bool _isSyncing = false;
  late AnimationController _syncAnimationController;
  final List<Map<String, String>> _supportedLocales = [
    {'code': 'en', 'name': 'English'},
    {'code': 'sw', 'name': 'Kiswahili'},
  ];

  @override
  void initState() {
    super.initState();
    _selectedCurrency = serviceLocator.settingsService.selectedCurrency;
    _selectedLocale = serviceLocator.settingsService.currentLocale;
    _syncAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );
    // Start continuous rotation
    _syncAnimationController.repeat();
  }

  @override
  void dispose() {
    _syncAnimationController.dispose();
    super.dispose();
  }

  String _getLocaleName(Locale locale) {
    for (var supportedLocale in _supportedLocales) {
      if (supportedLocale['code'] == locale.languageCode) {
        return supportedLocale['name']!;
      }
    }
    return 'Unknown';
  }

  // Sync settings across devices
  Future<void> _syncSettings() async {
    if (!serviceLocator.authService.isLoggedIn) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please log in to sync settings'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSyncing = true;
    });
    // No need to start animation as it's already continuously rotating

    try {
      final success = await serviceLocator.settingsService.saveSettingsToDatabase();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success 
              ? 'Settings synced successfully' 
              : 'Failed to sync settings'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error syncing settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSyncing = false;
        });
        // No need to stop animation as we want it to continuously rotate
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations?.translate('settings') ?? 'Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Language settings
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'Language',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('App Language'),
                  subtitle: Text(_getLocaleName(_selectedLocale)),
                  leading: const Icon(Icons.language),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: _showLanguagePicker,
                ),
              ],
            ),
          ),

          // Currency setting
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    localizations?.translate('currency') ?? 'Currency',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: Text(
                    localizations?.translate('defaultCurrency') ??
                        'Default Currency',
                  ),
                  subtitle: Text(
                    '${_selectedCurrency.name} (${_selectedCurrency.code}) ${_selectedCurrency.symbol}',
                  ),
                  leading: const Icon(Icons.currency_exchange),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    _showCurrencyPicker();
                  },
                ),
                // Currency example
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations?.translate('currencyExample') ??
                            'Example:',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        serviceLocator.settingsService.formatCurrency(1234.56),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Sync notice
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: const Text(
                      'User settings will be synced across all devices',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: _isSyncing ? null : _syncSettings,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: RotationTransition(
                        turns: _syncAnimationController,
                        child: Icon(
                          Icons.sync,
                          color: _isSyncing ? Theme.of(context).primaryColor : Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Legal Documents
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'Legal',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('Terms & Conditions'),
                  subtitle: const Text('View our terms of service'),
                  leading: Icon(
                    Icons.description,
                    color: Colors.blue.shade600,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const TermsAndConditionsScreen(),
                      ),
                    );
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Privacy Policy'),
                  subtitle: const Text('Learn how we protect your data'),
                  leading: Icon(
                    Icons.privacy_tip,
                    color: Colors.green.shade600,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // App Information
          Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 16, top: 16),
                  child: Text(
                    'About',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                ListTile(
                  title: const Text('Tenanta'),
                  subtitle: const Text('Property Management App\nVersion 1.0.102\nDeveloped by Creative DesignersKE'),
                  leading: Icon(
                    Icons.info_outline,
                    color: Colors.orange.shade600,
                  ),
                  isThreeLine: true,
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Changelog'),
                  subtitle: const Text('View app updates and release notes'),
                  leading: Icon(
                    Icons.history,
                    color: Colors.blue.shade600,
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () {
                    _showChangelog();
                  },
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('Contact Support'),
                  subtitle: const Text('<EMAIL>'),
                  leading: Icon(
                    Icons.support_agent,
                    color: Colors.purple.shade600,
                  ),
                  trailing: const Icon(Icons.email, size: 16),
                  onTap: () {
                    // You can implement email launching here if needed
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Contact us at: <EMAIL>'),
                        duration: Duration(seconds: 3),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguagePicker() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                _supportedLocales.map((locale) {
                  return ListTile(
                    title: Text(locale['name']!),
                    onTap: () async {
                      final newLocale = Locale(locale['code']!);

                      // Close dialog before async operation
                      Navigator.pop(dialogContext);

                      // Update locale in settings service
                      await serviceLocator.settingsService.setLocale(newLocale);
                      
                      // Update UI
                      if (mounted) {
                        setState(() {
                          _selectedLocale = newLocale;
                        });
                        
                        // Rebuild the entire app to apply language change
                        _restartApp();
                      }
                    },
                  );
                }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(dialogContext);
              },
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  // Method to restart app to apply language change
  void _restartApp() {
    // Need to restart the entire app to apply language changes
    // This is because the MaterialApp needs to rebuild with the new locale
    final context = this.context;
    
    // Navigate to dashboard and clear all previous routes
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/dashboard', 
      (route) => false,
    );
  }

  void _showCurrencyPicker() {
    showCurrencyPicker(
      context: context,
      showFlag: true,
      showCurrencyName: true,
      showCurrencyCode: true,
      onSelect: (Currency currency) async {
        // Update currency in settings service
        await serviceLocator.settingsService.setCurrency(currency);

        // Update UI
        if (mounted) {
          setState(() {
            _selectedCurrency = currency;
          });
        }
      },
    );
  }

  void _showChangelog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            constraints: const BoxConstraints(
              maxHeight: 600,
              maxWidth: 500,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with gradient
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.history,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Changelog',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'What\'s new in Tenanta',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(dialogContext),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Version 1.0.102
                        _buildVersionSection(
                          version: '1.0.102',
                          date: 'Current Release',
                          releaseType: 'LATEST',
                          isLatest: true,
                          changes: [
                            {'type': 'feature', 'text': 'Enhanced user interface and experience'},
                            {'type': 'improvement', 'text': 'Improved performance and stability'},
                            {'type': 'fix', 'text': 'Bug fixes and optimizations'},
                            {'type': 'feature', 'text': 'Updated app documentation'},
                            {'type': 'feature', 'text': 'Added comprehensive changelog feature'},
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Version 1.0.0
                        _buildVersionSection(
                          version: '1.0.0',
                          date: 'Initial Release',
                          releaseType: 'STABLE',
                          isLatest: false,
                          changes: [
                            {'type': 'feature', 'text': 'Initial release of Tenanta property management app'},
                            {'type': 'feature', 'text': 'Property listing and management features'},
                            {'type': 'feature', 'text': 'Tenant management system'},
                            {'type': 'feature', 'text': 'Payment tracking and rent collection'},
                            {'type': 'feature', 'text': 'Multi-language support (English & Kiswahili)'},
                            {'type': 'feature', 'text': 'Multi-currency support'},
                            {'type': 'feature', 'text': 'User authentication and security'},
                            {'type': 'feature', 'text': 'Settings synchronization across devices'},
                            {'type': 'feature', 'text': 'Legal documents integration'},
                            {'type': 'feature', 'text': 'Contact support functionality'},
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildVersionSection({
    required String version,
    required String date,
    required String releaseType,
    required bool isLatest,
    required List<Map<String, String>> changes,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: isLatest
          ? LinearGradient(
              colors: [
                Theme.of(context).primaryColor.withValues(alpha: 0.1),
                Theme.of(context).primaryColor.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            )
          : null,
        border: Border.all(
          color: isLatest ? Theme.of(context).primaryColor : Colors.grey.shade300,
          width: isLatest ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: isLatest
          ? [
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ]
          : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Version header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isLatest ? Theme.of(context).primaryColor : Colors.grey.shade400,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  isLatest ? Icons.new_releases : Icons.check_circle,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Version $version',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isLatest ? Theme.of(context).primaryColor : Colors.black87,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                          decoration: BoxDecoration(
                            color: isLatest ? Theme.of(context).primaryColor : Colors.grey.shade600,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            releaseType,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    Text(
                      date,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Changes list
          ...changes.map((change) {
            final changeType = change['type']!;
            final changeText = change['text']!;

            Color iconColor;
            IconData iconData;

            switch (changeType) {
              case 'feature':
                iconColor = Colors.green;
                iconData = Icons.add_circle;
                break;
              case 'improvement':
                iconColor = Colors.blue;
                iconData = Icons.trending_up;
                break;
              case 'fix':
                iconColor = Colors.orange;
                iconData = Icons.build_circle;
                break;
              default:
                iconColor = Colors.grey;
                iconData = Icons.circle;
            }

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    child: Icon(
                      iconData,
                      color: iconColor,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      changeText,
                      style: const TextStyle(
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
