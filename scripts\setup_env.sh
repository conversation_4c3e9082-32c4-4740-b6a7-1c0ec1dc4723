#!/bin/bash

# Setup script for Tenanta environment configuration
echo "Setting up Tenanta environment configuration..."

# Check if .env already exists
if [ -f ".env" ]; then
    echo "⚠️  .env file already exists!"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Setup cancelled."
        exit 0
    fi
fi

# Copy .env.example to .env
if [ -f ".env.example" ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
else
    echo "❌ .env.example file not found!"
    exit 1
fi

echo ""
echo "📝 Please edit the .env file and add your Supabase credentials:"
echo "   - SUPABASE_URL: Your Supabase project URL"
echo "   - SUPABASE_ANON_KEY: Your Supabase anonymous key"
echo ""
echo "You can find these values in your Supabase project settings."
echo ""
echo "🔒 Security reminder: Never commit the .env file to version control!"
echo ""
echo "Setup complete! Run 'flutter run' to start the app."
