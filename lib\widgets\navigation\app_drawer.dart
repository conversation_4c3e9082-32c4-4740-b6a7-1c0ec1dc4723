import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';
import '../../services/service_locator.dart';
import '../../main.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final user = serviceLocator.authService.currentUser;
    final userName = user?.userMetadata?['full_name'] ?? user?.email ?? 'User';
    final userEmail = user?.email ?? '';
    final localizations = AppLocalizations.of(context);
    
    // Helper function to ensure first letter is capitalized
    String capitalize(String text) {
      if (text.isEmpty) return text;
      return text[0].toUpperCase() + text.substring(1);
    }

    return Drawer(
      child: Column(
        children: [
          // Main drawer items in a ListView
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                UserAccountsDrawerHeader(
                  accountName: Text(
                    userName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  accountEmail: Text(userEmail),
                  currentAccountPicture: CircleAvatar(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: Text(
                      userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.inversePrimary,
                  ),
                  onDetailsPressed: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/profile');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.dashboard,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('dashboard') ?? 'Dashboard'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushReplacementNamed(context, '/dashboard');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.home_work,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('properties') ?? 'Properties'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/properties');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.meeting_room,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(capitalize(localizations?.translate('rooms') ?? 'Rooms')),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/rooms');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.people,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(capitalize(localizations?.translate('tenants') ?? 'Tenants')),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/tenants');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.payments,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('payments') ?? 'Payments'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/payments');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.receipt_long,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('expenses') ?? 'Expenses'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/expenses');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.campaign,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: const Text('Community Notices'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/community-notices');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.bar_chart,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: const Text('Reports'),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/reports');
                  },
                ),
                ExpansionTile(
                  leading: Icon(
                    Icons.add_circle,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('quickActions') ?? 'Quick Actions'),
                  ),
                  children: [
                    ListTile(
                      leading: const Icon(Icons.receipt_long),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: Text(
                        capitalize(localizations?.translate('addBill') ?? 'Add Bill'),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/select-tenant-for-bill');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.people),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Create Group Bill'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/multi-tenant-bill');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.payment),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: Text(
                        capitalize(localizations?.translate('recordPayment') ?? 'Record Payment'),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(
                          context,
                          '/select-tenant-for-payment',
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.add_card),
                      contentPadding: const EdgeInsets.only(
                        left: 72.0,
                        right: 16.0,
                      ),
                      title: const Text('Add Expense'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/expenses/add');
                      },
                    ),
                  ],
                ),
                const Divider(),
                ListTile(
                  leading: Icon(
                    Icons.person,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(capitalize(localizations?.translate('profile') ?? 'Profile')),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/profile');
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.settings,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    capitalize(localizations?.translate('settings') ?? 'Settings'),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, '/settings');
                  },
                ),
              ],
            ),
          ),
          // Sign out button at the bottom
          Container(
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
              ),
            ),
            child: ListTile(
              leading: Icon(
                Icons.logout,
                color: Theme.of(context).colorScheme.error,
              ),
              title: Text(
                capitalize(localizations?.translate('signOut') ?? 'Sign Out'),
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              onTap: () async {
                Navigator.pop(context);
                await serviceLocator.authService.signOut();
                if (context.mounted) {
                  navigatorKey.currentState?.pushReplacementNamed('/auth');
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
