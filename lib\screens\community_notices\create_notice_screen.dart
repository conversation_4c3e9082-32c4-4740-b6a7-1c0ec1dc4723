import 'package:flutter/material.dart';
import '../../models/community_notice/community_notice.dart';
import '../../services/community_notice/community_notice_service.dart';
import '../../services/property/property_service.dart';
import '../../services/service_locator.dart';
import '../../widgets/loading_indicator.dart';
import '../../widgets/error_dialog.dart';

class CreateNoticeScreen extends StatefulWidget {
  final String? propertyId;
  final CommunityNotice? notice; // For editing existing notice

  const CreateNoticeScreen({
    super.key,
    this.propertyId,
    this.notice,
  });

  @override
  State<CreateNoticeScreen> createState() => _CreateNoticeScreenState();
}

class _CreateNoticeScreenState extends State<CreateNoticeScreen> {
  final _formKey = GlobalKey<FormState>();
  final CommunityNoticeService _noticeService = CommunityNoticeService();
  final PropertyService _propertyService = PropertyService();

  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  
  List<Map<String, dynamic>> _properties = [];
  List<String> _selectedPropertyIds = [];
  NoticeType _selectedType = NoticeType.general;
  NoticePriority _selectedPriority = NoticePriority.medium;
  DateTime? _expiresAt;
  bool _isLoading = false;
  bool _isLoadingProperties = true;

  bool get _isEditing => widget.notice != null;

  @override
  void initState() {
    super.initState();
    if (widget.propertyId != null) {
      _selectedPropertyIds = [widget.propertyId!];
    }
    _loadProperties();

    if (_isEditing) {
      _populateFormForEditing();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _populateFormForEditing() {
    final notice = widget.notice!;
    _titleController.text = notice.title;
    _contentController.text = notice.content;
    _selectedPropertyIds = [notice.propertyId];
    _selectedType = notice.type;
    _selectedPriority = notice.priority;
    _expiresAt = notice.expiresAt;
  }

  Future<void> _loadProperties() async {
    try {
      final properties = await _propertyService.getAllProperties();
      setState(() {
        _properties = properties.map((p) => {
          'id': p.id,
          'name': p.name,
        }).toList();
        _isLoadingProperties = false;
      });
    } catch (e) {
      setState(() => _isLoadingProperties = false);
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ErrorDialog(
            title: 'Error Loading Properties',
            message: e.toString(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Notice' : 'Create Notice'),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveNotice,
            child: Text(_isEditing ? 'Update' : 'Create'),
          ),
        ],
      ),
      body: _isLoadingProperties
          ? const LoadingIndicator()
          : Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPropertySelector(),
                    const SizedBox(height: 24),
                    _buildTitleField(),
                    const SizedBox(height: 24),
                    _buildContentField(),
                    const SizedBox(height: 24),
                    _buildTypeAndPriorityRow(),
                    const SizedBox(height: 24),
                    _buildExpirationSection(),
                    const SizedBox(height: 32),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildPropertySelector() {
    if (_properties.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(Icons.warning, color: Colors.orange, size: 48),
              const SizedBox(height: 8),
              const Text(
                'No Properties Available',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              const Text(
                'You need to create at least one property before creating notices.',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Properties *',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            Text(
              '${_selectedPropertyIds.length} selected',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Selected properties display
        if (_selectedPropertyIds.isNotEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _selectedPropertyIds.map((propertyId) {
                Map<String, dynamic> property;
                try {
                  property = _properties.firstWhere(
                    (p) => p['id'] == propertyId,
                  );
                } catch (e) {
                  property = <String, dynamic>{'name': 'Unknown Property'};
                }
                return Chip(
                  label: Text(
                    property['name'] as String,
                    style: const TextStyle(fontSize: 12),
                  ),
                  deleteIcon: const Icon(Icons.close, size: 16),
                  onDeleted: () {
                    setState(() {
                      _selectedPropertyIds.remove(propertyId);
                    });
                  },
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 8),
        ],

        // Property selection button
        OutlinedButton.icon(
          onPressed: _showPropertySelectionDialog,
          icon: const Icon(Icons.add),
          label: Text(_selectedPropertyIds.isEmpty ? 'Select Properties' : 'Add More Properties'),
          style: OutlinedButton.styleFrom(
            minimumSize: const Size(double.infinity, 48),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Title *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            hintText: 'Enter notice title',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            prefixIcon: const Icon(Icons.title),
          ),
          maxLength: 255,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a title';
            }
            if (value.trim().length < 3) {
              return 'Title must be at least 3 characters long';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildContentField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Content *',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _contentController,
          decoration: InputDecoration(
            hintText: 'Enter notice content',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            prefixIcon: const Icon(Icons.description),
            alignLabelWithHint: true,
          ),
          maxLines: 6,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter content';
            }
            if (value.trim().length < 10) {
              return 'Content must be at least 10 characters long';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildTypeAndPriorityRow() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Type *',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<NoticeType>(
                value: _selectedType,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.category),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                isExpanded: true,
                items: NoticeType.values.map((type) => DropdownMenuItem<NoticeType>(
                  value: type,
                  child: Text(
                    type.displayName,
                    overflow: TextOverflow.ellipsis,
                  ),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Priority *',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<NoticePriority>(
                value: _selectedPriority,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.priority_high),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                isExpanded: true,
                items: NoticePriority.values.map((priority) => DropdownMenuItem<NoticePriority>(
                  value: priority,
                  child: Text(
                    priority.displayName,
                    overflow: TextOverflow.ellipsis,
                  ),
                )).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPriority = value;
                    });
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExpirationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Expiration (Optional)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.grey[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _expiresAt == null
                            ? 'No expiration date set'
                            : 'Expires on ${_formatDate(_expiresAt!)}',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    if (_expiresAt != null)
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _expiresAt = null;
                          });
                        },
                        tooltip: 'Remove expiration',
                      ),
                  ],
                ),
                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _selectExpirationDate,
                        icon: const Icon(Icons.calendar_today),
                        label: Text(_expiresAt == null ? 'Set Expiration Date' : 'Change Date'),
                      ),
                    ),
                  ],
                ),

                if (_expiresAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Notice will be automatically archived after this date',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    if (_properties.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveNotice,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Text(
                    _isEditing ? 'Update Notice' : 'Create Notice',
                    style: const TextStyle(fontSize: 16),
                  ),
          ),
        ),
        const SizedBox(height: 12),

        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectExpirationDate() async {
    final now = DateTime.now();
    final initialDate = _expiresAt ?? now.add(const Duration(days: 7));

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate.isAfter(now) ? initialDate : now.add(const Duration(days: 1)),
      firstDate: now,
      lastDate: now.add(const Duration(days: 365)),
      helpText: 'Select expiration date',
    );

    if (selectedDate != null && mounted) {
      final selectedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(initialDate),
        helpText: 'Select expiration time',
      );

      if (selectedTime != null) {
        setState(() {
          _expiresAt = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            selectedTime.hour,
            selectedTime.minute,
          );
        });
      }
    }
  }

  Future<void> _saveNotice() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPropertyIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one property'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final currentUser = serviceLocator.authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Get user profile for author name
      final userProfile = await serviceLocator.userProfileService.getCurrentUserProfile();
      final authorName = userProfile?.fullName ?? currentUser.email ?? 'Unknown User';

      if (_isEditing) {
        // Update existing notice (only supports single property for editing)
        final updatedNotice = widget.notice!.copyWith(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          type: _selectedType,
          priority: _selectedPriority,
          propertyId: _selectedPropertyIds.first,
          expiresAt: _expiresAt,
          updatedAt: DateTime.now(),
        );

        await _noticeService.updateNotice(updatedNotice);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notice updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Create new notices (one for each selected property)
        int createdCount = 0;

        for (final propertyId in _selectedPropertyIds) {
          final newNotice = CommunityNotice.create(
            title: _titleController.text.trim(),
            content: _contentController.text.trim(),
            type: _selectedType,
            priority: _selectedPriority,
            propertyId: propertyId,
            authorId: currentUser.id,
            authorName: authorName,
            expiresAt: _expiresAt,
          );

          await _noticeService.createNotice(newNotice);
          createdCount++;
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                createdCount == 1
                    ? 'Notice created successfully'
                    : '$createdCount notices created successfully',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      if (mounted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => ErrorDialog(
            title: _isEditing ? 'Error Updating Notice' : 'Error Creating Notice',
            message: e.toString(),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showPropertySelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => PropertySelectionDialog(
        properties: _properties,
        selectedPropertyIds: List.from(_selectedPropertyIds),
        onSelectionChanged: (selectedIds) {
          setState(() {
            _selectedPropertyIds = selectedIds;
          });
        },
      ),
    );
  }
}

class PropertySelectionDialog extends StatefulWidget {
  final List<Map<String, dynamic>> properties;
  final List<String> selectedPropertyIds;
  final Function(List<String>) onSelectionChanged;

  const PropertySelectionDialog({
    super.key,
    required this.properties,
    required this.selectedPropertyIds,
    required this.onSelectionChanged,
  });

  @override
  State<PropertySelectionDialog> createState() => _PropertySelectionDialogState();
}

class _PropertySelectionDialogState extends State<PropertySelectionDialog> {
  late List<String> _tempSelectedIds;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tempSelectedIds = List.from(widget.selectedPropertyIds);
  }

  @override
  Widget build(BuildContext context) {
    final filteredProperties = widget.properties.where((property) {
      final name = property['name'] as String;
      return name.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return AlertDialog(
      title: const Text('Select Properties'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Search properties...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // Select all/none buttons
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds = filteredProperties
                          .map((p) => p['id'] as String)
                          .toList();
                    });
                  },
                  child: const Text('Select All'),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _tempSelectedIds.clear();
                    });
                  },
                  child: const Text('Select None'),
                ),
                const Spacer(),
                Text(
                  '${_tempSelectedIds.length} selected',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Properties list
            Expanded(
              child: ListView.builder(
                itemCount: filteredProperties.length,
                itemBuilder: (context, index) {
                  final property = filteredProperties[index];
                  final propertyId = property['id'] as String;
                  final propertyName = property['name'] as String;
                  final isSelected = _tempSelectedIds.contains(propertyId);

                  return CheckboxListTile(
                    title: Text(propertyName),
                    value: isSelected,
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          _tempSelectedIds.add(propertyId);
                        } else {
                          _tempSelectedIds.remove(propertyId);
                        }
                      });
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _tempSelectedIds.isEmpty
              ? null
              : () {
                  widget.onSelectionChanged(_tempSelectedIds);
                  Navigator.pop(context);
                },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}
