# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.103] - 2025-01-22

### Added
- **Combined Property Notices**: When editing a notice and selecting multiple properties, the system now creates a single combined property notice instead of separate individual notices for each property
  - Enhanced `CommunityNotice` model with support for combined property notices through metadata
  - Added `isCombinedPropertyNotice` property to identify multi-property notices
  - Added `allPropertyIds` and `allPropertyNames` getters to retrieve all associated properties
  - Added `propertyCount` getter to show the number of properties affected
  - Added `propertyDisplayText` getter for user-friendly property display
  - New `createCombined()` factory method for creating combined property notices
  - New `toCombinedNotice()` method for converting existing notices to combined notices

### Enhanced
- **Notice Creation and Editing**:
  - Updated `CommunityNoticeService` with `createCombinedNotice()` and `convertToCombinedNotice()` methods
  - Modified edit notice logic in `CreateNoticeScreen` to detect multiple property selection and create combined notices
  - Enhanced property selection during editing to support multiple properties for existing notices

- **User Interface Improvements**:
  - Updated notice detail screen to display combined property information with appropriate icons
  - Enhanced notice list cards to show multi-property indicators and property counts
  - Updated property notices widget to display combined notice indicators
  - Added visual distinction between single and multi-property notices across all UI components

### Technical
- Added comprehensive unit tests for combined property notice functionality
- Added widget tests for UI display of combined property notices
- Maintained backward compatibility with existing single-property notices
- Utilized existing database schema with metadata field for storing additional property information

## [1.0.0] - 2025-01-22

### Added
- Initial release of Tenanta property management system
- Property management with CRUD operations
- Room management and tenant assignment
- Community notices system
- Billing and payment tracking
- Expense management
- User authentication and profiles
- Dashboard with key metrics and insights
